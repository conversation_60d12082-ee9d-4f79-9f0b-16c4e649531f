# Session Tracking + Auto-Start Logging Implementation Plan


## Overview

This document outlines the integrated implementation plan for Session Tracking and Auto-Start Logging features in the GA Modbus Python Application. These features work together to provide comprehensive monitoring session management with intelligent automatic data collection.

## Combined Feature Architecture

### Core Integration Concept
- **Session Tracking**: Manages monitoring session lifecycle and data organization
- **Auto-Start Logging**: Automatically triggers sessions based on current flow thresholds
- **Unified Management**: Single system handling both manual and automatic session creation

### Enhanced Session Data Model

```python
class Session:
    def __init__(self):
        # Core session metadata
        self.session_id: str = uuid.uuid4()
        self.start_time: datetime = None
        self.end_time: datetime = None
        self.duration_seconds: int = 0
        self.status: str = 'pending'  # 'pending', 'active', 'completed', 'interrupted'
        
        # Trigger information
        self.trigger_type: str = 'manual'  # 'manual', 'auto_charge', 'auto_discharge'
        self.auto_start_threshold: float = None
        self.auto_end_enabled: bool = False
        self.recovery_phase: bool = False
        
        # Data association
        self.csv_files: List[str] = []
        self.connection_params: dict = {}
        
        # Session statistics
        self.data_summary: dict = {
            'total_records': 0,
            'data_start_time': None,
            'data_end_time': None,
            'avg_cell_voltage': 0.0,
            'avg_pack_voltage': 0.0,
            'monitoring_gaps': 0
        }
        
        # Auto-start specific tracking
        self.charge_discharge_events: List[dict] = []
        
        # User interaction
        self.notes: str = ""
        self.created_by: str = "GUI"  # 'GUI' or 'CLI'
```

### Auto-Start Configuration Schema

```python
class AutoStartConfig:
    def __init__(self):
        # Auto-start mode control
        self.auto_log_mode: str = 'OFF'  # 'OFF', 'ENABLE'
        
        # Threshold settings
        self.auto_log_charging_threshold: float = 0.5  # Amps
        self.auto_log_discharging_threshold: float = -0.5  # Amps
        
        # Feature enablement
        self.auto_log_charge_enable: bool = True
        self.auto_log_discharge_enable: bool = True
        
        # Auto-end timing
        self.auto_log_charge_end_time: int = 30  # Minutes
        self.auto_log_discharge_end_time: int = 30  # Minutes
        
        # Recovery logging
        self.pack_recovery_logging: bool = True
        self.recovery_duration_minutes: int = 15
```

## Implementation Phases

### Phase 1: Core Session Manager (Week 1)

#### 1.1 SessionManager Class (`src/core/session_manager.py`)
```python
class SessionManager:
    def __init__(self, settings: QSettings):
        self.settings = settings
        self.current_session: Session = None
        self.auto_config: AutoStartConfig = AutoStartConfig()
        self.load_auto_config()
    
    def start_manual_session(self, connection_params: dict) -> str:
        """User-initiated session start"""
        
    def start_auto_session(self, trigger_type: str, current_value: float) -> str:
        """Auto-triggered session start"""
        
    def end_session(self, session_id: str = None) -> dict:
        """End current or specified session"""
        
    def check_auto_start_conditions(self, current_value: float) -> bool:
        """Evaluate if auto-start should trigger"""
        
    def update_session_data(self, data: dict):
        """Update current session with new data"""
```

#### 1.2 Integration Points
- **BMSMainWindow**: Add session_manager instance
- **ModbusWorker**: Add session data updates and auto-start checking
- **Settings**: Extend QSettings with session and auto-start configuration

### Phase 2: Auto-Start Logic Integration (Week 2)

#### 2.1 Current Monitoring Integration
```python
# In ModbusWorker.handle_new_data()
def handle_new_data(self, data):
    # Existing data processing...
    
    # Extract current value with validation
    current_value = self.extract_validated_current(data)
    
    # Check auto-start conditions
    if self.session_manager.check_auto_start_conditions(current_value):
        session_id = self.session_manager.start_auto_session(
            trigger_type=self.determine_trigger_type(current_value),
            current_value=current_value
        )
        self.session_started.emit(session_id)
    
    # Update active session
    if self.session_manager.current_session:
        self.session_manager.update_session_data({
            'timestamp': datetime.now(),
            'current': current_value,
            'data': data
        })
```

#### 2.2 Threshold Logic
```python
def check_auto_start_conditions(self, current_value: float) -> bool:
    if self.auto_config.auto_log_mode != 'ENABLE':
        return False
    
    if self.current_session is not None:
        return False  # Session already active
    
    # Check charging threshold
    if (self.auto_config.auto_log_charge_enable and 
        current_value >= self.auto_config.auto_log_charging_threshold):
        return True
    
    # Check discharging threshold  
    if (self.auto_config.auto_log_discharge_enable and
        current_value <= self.auto_config.auto_log_discharging_threshold):
        return True
    
    return False
```

### Phase 3: GUI Integration (Week 3)

#### 3.1 New Session Management Tab
- **Session History Table**: Display all sessions with trigger type indicators
- **Active Session Panel**: Current session status and real-time metrics
- **Auto-Start Controls**: Enable/disable and threshold configuration
- **Session Actions**: Manual start/stop, add notes, export

#### 3.2 Enhanced Settings Tab
```python
class AutoStartSettingsWidget(QWidget):
    def __init__(self):
        # Auto-start mode selection
        self.auto_mode_combo = QComboBox()
        self.auto_mode_combo.addItems(['OFF', 'ENABLE'])
        
        # Threshold inputs
        self.charge_threshold_spin = QDoubleSpinBox()
        self.discharge_threshold_spin = QDoubleSpinBox()
        
        # Enable checkboxes
        self.charge_enable_check = QCheckBox("Enable Auto-Start on Charging")
        self.discharge_enable_check = QCheckBox("Enable Auto-Start on Discharging")
        
        # End time settings
        self.charge_end_time_spin = QSpinBox()
        self.discharge_end_time_spin = QSpinBox()
        
        # Recovery logging
        self.recovery_logging_check = QCheckBox("Enable Pack Recovery Logging")
```

#### 3.3 Real-time View Enhancements
- **Session Indicator**: Show current session ID and trigger type
- **Auto-Start Status**: Visual indicators for auto-start mode and thresholds
- **Threshold Visualization**: Current value vs threshold on plots

### Phase 4: Advanced Features (Week 4)

#### 4.1 Session Analytics
- **Session Comparison**: Compare metrics across multiple sessions
- **Trend Analysis**: Battery health trends over time
- **Auto-Start Optimization**: Suggest optimal thresholds based on history

#### 4.2 Data Export and Reporting
- **Session Reports**: Comprehensive session summaries
- **CSV Export**: Session metadata and statistics
- **Historical Analysis**: Long-term monitoring patterns

## Technical Implementation Details

### QSettings Integration

```python
# Session storage keys
SESSIONS_BASE_KEY = "sessions"
CURRENT_SESSION_KEY = f"{SESSIONS_BASE_KEY}/current_session_id"
SESSION_COUNT_KEY = f"{SESSIONS_BASE_KEY}/session_count"
AUTO_CONFIG_KEY = "auto_start_config"

# Session data keys
def get_session_key(session_id: str, field: str) -> str:
    return f"{SESSIONS_BASE_KEY}/sessions/{session_id}/{field}"
```

### Auto-Start State Machine

```python
class AutoStartState:
    DISABLED = "disabled"
    MONITORING = "monitoring"          # Watching for thresholds
    SESSION_ACTIVE = "session_active"  # Auto-started session running
    RECOVERY_PHASE = "recovery_phase"  # Post-event recovery logging
    COOLDOWN = "cooldown"             # Preventing rapid re-triggering
```

### CSV File Association

```python
def associate_csv_with_session(self, csv_filename: str):
    """Link CSV file to current session"""
    if self.current_session:
        self.current_session.csv_files.append(csv_filename)
        self.save_session_data(self.current_session)
        
        # Add session metadata to CSV header comment
        self.add_session_header_to_csv(csv_filename, self.current_session)
```

## Integration with Existing Code

### Key Modification Points

1. **BMSMainWindow.__init__()** (line ~1300):
   - Add `self.session_manager = SessionManager(self.settings)`
   - Initialize auto-start configuration UI

2. **BMSMainWindow.start_monitoring()** (line 1535):
   - Create manual session via session manager
   - Update UI to show session status

3. **BMSMainWindow.stop_monitoring()** (line 1566):
   - End current session
   - Save session summary

4. **ModbusWorker.handle_new_data()** (line 1581):
   - Add auto-start condition checking
   - Update session data and statistics

5. **Settings Tab** (line 1400+):
   - Add auto-start configuration widgets
   - Save/load auto-start preferences

### Backward Compatibility

- Existing CSV files remain unchanged
- QSettings keys use new namespace to avoid conflicts
- GUI layout accommodates new session tab without disrupting existing functionality
- CLI mode continues to work independently

## Testing Strategy

### Unit Tests
- SessionManager class functionality
- Auto-start threshold logic
- Session data model validation
- QSettings integration

### Integration Tests
- GUI session creation workflows
- Auto-start triggering scenarios
- CSV file association
- Settings persistence

### Edge Case Testing
- Rapid current fluctuations around thresholds
- Application restart during active sessions
- Multiple threshold crossings
- Network interruptions during auto-sessions

## Risk Mitigation

### Performance Considerations
- Auto-start checking adds minimal overhead to data processing loop
- Session data stored incrementally to avoid large QSettings writes
- GUI updates throttled to prevent UI blocking

### Data Integrity
- Session metadata backed up to JSON files
- CSV file association recoverable from filename patterns
- Graceful handling of corrupted session data

### User Experience
- Auto-start can be completely disabled
- Clear visual indicators for auto vs manual sessions
- Session recovery after unexpected application termination

## Success Metrics

1. **Functionality**: 
   - Auto-start triggers correctly on threshold crossings
   - Session data accurately captured and persisted
   - GUI provides clear session management interface

2. **Performance**:
   - No noticeable impact on real-time data acquisition
   - Responsive UI during session operations
   - Minimal memory footprint for session storage

3. **Usability**:
   - Intuitive auto-start configuration
   - Clear session history and analytics
   - Seamless integration with existing workflows

## Deployment Plan

1. **Feature Branch**: Complete implementation in `feature/session-tracking`
2. **Testing**: Comprehensive testing with various battery scenarios
3. **Documentation**: Update user documentation and help system
4. **Staging**: Deploy to test environment for validation
5. **Production**: Merge to main branch with version bump to v1.2.0